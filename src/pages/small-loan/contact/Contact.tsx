import { zod<PERSON><PERSON><PERSON>ver } from '@hookform/resolvers/zod';
import { PoliticalExposure } from 'api/core/generated';
import {
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_CONTACT_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
  REDIRECT_URLS,
} from 'app-constants';
import { AppExternalLink, AppLocalizationComponent } from 'components';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import { FormInputField } from 'components/form/form-input-field';
import { FormPhoneField } from 'components/form/form-phone-field';
import { FormSelectField } from 'components/form/form-select-field/FormSelectField';
import { WarningNotification } from 'components/notification';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { useRootContext } from 'context/root';
import { CONTACT_INFO } from 'environment';
import { useContactPageLogic } from 'hooks/page-logic/small-loan';
import type { AnyObject } from 'models';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import * as z from 'zod';

const ContactFormSchema = z.object({
  [FormFieldNames.name]: z.string(),
  [FormFieldNames.email]: z.string().email(),
  [FormFieldNames.phone]: z.string(),
  [FormFieldNames.address]: z.string(),
  [FormFieldNames.city]: z.string(),
  [FormFieldNames.postCode]: z.string(),
  [FormFieldNames.iban]: z.string(),
  [FormFieldNames.politicalExposure]: z.string(),
  [FormFieldNames.conditionsAgreement]: z.boolean(),
  [FormFieldNames.conditionsAndPensionAgreement]: z.boolean(),
  [FormFieldNames.newsletterAgreement]: z.boolean(),
});

export type ContactFormType = z.infer<typeof ContactFormSchema>;

const Contact = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.contact);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const { contactName } = CONTACT_INFO;
  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const {
    contactPageLoaded,
    contactPageFormConfig,
    onContactFormSubmit,
    politicalExposureOptions,
    visiblePageAttributes,
    userInfoValidationErrors,
  } = useContactPageLogic();

  // Create form using the config from the hook
  const form = useForm<ContactFormType>({
    resolver: zodResolver(ContactFormSchema),
    defaultValues: contactPageFormConfig.defaultValues,
  });

  // Reset form values when application data loads and contactPageFormConfig changes
  useEffect(() => {
    if (contactPageLoaded && contactPageFormConfig.defaultValues) {
      form.reset(contactPageFormConfig.defaultValues);
    }
  }, [contactPageFormConfig.defaultValues, contactPageLoaded, form]);

  // Watch form values for conditional rendering
  const politicalExposureSelectValue = form.watch(
    FormFieldNames.politicalExposure,
  );
  const termsAndConditionsCheckboxValue = form.watch(
    FormFieldNames.conditionsAgreement,
  );
  const termsAndPensionCheckboxValue = form.watch(
    FormFieldNames.conditionsAndPensionAgreement,
  );

  if (!contactPageLoaded) {
    return <div>Loading...</div>;
  }

  const termsLabel = (
    <AppLocalizationComponent
      components={{
        site_link: (
          <AppExternalLink
            to={(REDIRECT_URLS.termsPageUrs as AnyObject)[i18n.language]}
          />
        ),
      }}
      locizeKey={
        LOCIZE_CONTACT_TRANSLATION_KEYS.termsAndConditionsCheckboxLabel
      }
      t={t}
      values={{
        contact_name: contactName,
      }}
    />
  );

  const termsAndPensionLabel = (
    <AppLocalizationComponent
      components={{
        site_link: (
          <AppExternalLink
            to={(REDIRECT_URLS.termsPageUrs as AnyObject)[i18n.language]}
          />
        ),
      }}
      locizeKey={
        LOCIZE_CONTACT_TRANSLATION_KEYS.termsAndConditionsAndPensionCheckboxLabel
      }
      t={t}
      values={{
        contact_name: contactName,
      }}
    />
  );

  const submitButtonDisabled =
    (visiblePageAttributes[PageAttributeNames.termsAndConditionsCheckbox] &&
      !termsAndConditionsCheckboxValue) ||
    (visiblePageAttributes[
      PageAttributeNames.termsAndConditionsAndPensionCheckbox
    ] &&
      !termsAndPensionCheckboxValue);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) =>
          onContactFormSubmit(data, form as any),
        )}
        className="grid w-full gap-2"
      >
        {visiblePageAttributes[PageAttributeNames.name] ? (
          <FormInputField
            control={form.control}
            name={FormFieldNames.name}
            label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.nameFieldLabel)}
            disabled
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.email] ? (
          <FormInputField
            control={form.control}
            name={FormFieldNames.email}
            label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.emailFieldLabel)}
            invalid={
              userInfoValidationErrors[FormFieldNames.email] ||
              !!form.getFieldState(FormFieldNames.email).error
            }
            disabled={form.formState.isSubmitting}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.phoneNumber] ? (
          <FormPhoneField<ContactFormType>
            name={FormFieldNames.phone}
            control={form.control}
            label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.phoneFieldLabel)}
            invalid={
              userInfoValidationErrors[FormFieldNames.phone] ||
              !!form.getFieldState(FormFieldNames.phone).error
            }
            disabled={form.formState.isSubmitting}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.address] ? (
          <FormInputField
            control={form.control}
            name={FormFieldNames.address}
            label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.addressFieldLabel)}
            invalid={
              userInfoValidationErrors[FormFieldNames.address] ||
              !!form.getFieldState(FormFieldNames.address).error
            }
            disabled={form.formState.isSubmitting}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.city] ? (
          <FormInputField
            control={form.control}
            name={FormFieldNames.city}
            label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.cityFieldLabel)}
            invalid={
              userInfoValidationErrors[FormFieldNames.city] ||
              !!form.getFieldState(FormFieldNames.city).error
            }
            disabled={form.formState.isSubmitting}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.postalCode] ? (
          <FormInputField
            control={form.control}
            name={FormFieldNames.postCode}
            label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.postalCodeFieldLabel)}
            invalid={
              userInfoValidationErrors[FormFieldNames.postCode] ||
              !!form.getFieldState(FormFieldNames.postCode).error
            }
            disabled={form.formState.isSubmitting}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.politicalExposureSelect] ? (
          <>
            <FormSelectField<ContactFormType>
              name={FormFieldNames.politicalExposure}
              control={form.control}
              label={t(
                LOCIZE_CONTACT_TRANSLATION_KEYS.politicallyExposedPersonFieldLabel,
              )}
              options={politicalExposureOptions}
              info={t(
                LOCIZE_CONTACT_TRANSLATION_KEYS.politicallyExposedPersonTooltipLabel,
              )}
              invalid={
                userInfoValidationErrors[FormFieldNames.politicalExposure] ||
                !!form.getFieldState(FormFieldNames.politicalExposure).error
              }
              disabled={
                form.formState.isSubmitting || !politicalExposureOptions.length
              }
            />

            {politicalExposureSelectValue !== PoliticalExposure.NONE ? (
              <WarningNotification>
                {t(
                  LOCIZE_CONTACT_TRANSLATION_KEYS.politicallyExposedPersonDisclaimerLabel,
                )}
              </WarningNotification>
            ) : null}
          </>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.iban] ? (
          <FormInputField<ContactFormType>
            name={FormFieldNames.iban}
            control={form.control}
            label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.ibanFieldLabel)}
            invalid={
              userInfoValidationErrors[FormFieldNames.iban] ||
              !!form.getFieldState(FormFieldNames.iban).error
            }
            disabled={form.formState.isSubmitting}
          />
        ) : null}

        {visiblePageAttributes[
          PageAttributeNames.termsAndConditionsCheckbox
        ] ? (
          <FormCheckboxField<ContactFormType>
            containerClassName="mt-2.5 px-2.5"
            name={FormFieldNames.conditionsAgreement}
            control={form.control}
            label={termsLabel}
            disabled={form.formState.isSubmitting}
            invalid={
              userInfoValidationErrors[FormFieldNames.conditionsAgreement] ||
              !!form.getFieldState(FormFieldNames.conditionsAgreement).error
            }
          />
        ) : null}

        {visiblePageAttributes[
          PageAttributeNames.termsAndConditionsAndPensionCheckbox
        ] ? (
          <FormCheckboxField<ContactFormType>
            containerClassName="mt-2.5 px-2.5"
            name={FormFieldNames.conditionsAndPensionAgreement}
            control={form.control}
            label={termsAndPensionLabel}
            disabled={form.formState.isSubmitting}
            invalid={
              userInfoValidationErrors[
                FormFieldNames.conditionsAndPensionAgreement
              ] ||
              !!form.getFieldState(FormFieldNames.conditionsAndPensionAgreement)
                .error
            }
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.newsletterCheckbox] ? (
          <FormCheckboxField<ContactFormType>
            name={FormFieldNames.newsletterAgreement}
            control={form.control}
            label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.newsletterCheckboxLabel, {
              contact_name: contactName,
            })}
            disabled={form.formState.isSubmitting}
            invalid={
              userInfoValidationErrors[FormFieldNames.newsletterAgreement] ||
              !!form.getFieldState(FormFieldNames.newsletterAgreement).error
            }
          />
        ) : null}

        <Button
          className="mt-8"
          disabled={
            submitButtonDisabled ||
            (!form.formState.isSubmitting && pageUrlAndNavigationProcessing)
          }
          loading={form.formState.isSubmitting}
          type="submit"
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        </Button>
        <Button
          fullWidth
          className="mt-2"
          variant="white"
          loading={
            !form.formState.isSubmitting && pageUrlAndNavigationProcessing
          }
          disabled={form.formState.isSubmitting}
          onClick={() => {
            getPageUrlAndNavigate(false);
          }}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
      </form>
    </Form>
  );
};

export default Contact;
